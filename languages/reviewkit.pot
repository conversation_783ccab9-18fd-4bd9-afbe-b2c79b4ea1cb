# Copyright (C) 2025 GutenSuite
# This file is distributed under the GPL-2.0-or-later.
msgid ""
msgstr ""
"Project-Id-Version: Reviewkit 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/reviewkit\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-07-31T15:25:40+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: reviewkit\n"

#. Plugin Name of the plugin
#: reviewkit.php
#: includes/Admin/class-menu.php:22
#: includes/Admin/class-menu.php:23
msgid "Reviewkit"
msgstr ""

#. Description of the plugin
#: reviewkit.php
msgid "Automatically sync and display your Trustpilot reviews on your WordPress site with customizable widgets, trust badges, and real-time updates. Boost credibility and conversions by showcasing genuine customer testimonials anywhere on your site with easy shortcodes and flexible layouts."
msgstr ""

#. Author of the plugin
#: reviewkit.php
msgid "GutenSuite"
msgstr ""

#: react_app/components/containers/reviews/ReviewList.jsx:63
#: assets/js/reviewkit.core.min.js:263
msgid ">No reviews available"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:51
#: assets/js/reviewkit.core.min.js:514
msgid "No business details found"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:85
#: assets/js/reviewkit.core.min.js:548
msgid "Claimed"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:90
#: assets/js/reviewkit.core.min.js:553
msgid "Verified"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:114
#: assets/js/reviewkit.core.min.js:577
msgid "Based on"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:115
#: react_app/components/shortcode-previews/MicroButtonPreview.jsx:21
#: react_app/components/shortcode-previews/MiniTrustboxPreview.jsx:48
#: react_app/components/shortcode-previews/StarterTrustboxPreview.jsx:24
#: assets/js/reviewkit.core.min.js:578
#: assets/js/reviewkit.core.min.js:1252
#: assets/js/reviewkit.core.min.js:1486
#: assets/js/reviewkit.core.min.js:1519
msgid "reviews"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:127
#: assets/js/reviewkit.core.min.js:590
msgid "Visit Website"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:137
#: assets/js/reviewkit.core.min.js:600
msgid "Reply Rate"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:145
#: assets/js/reviewkit.core.min.js:608
msgid "Avg. Reply Time"
msgstr ""

#: react_app/components/containers/reviews/TrustpilotBusinessPage.jsx:149
#: assets/js/reviewkit.core.min.js:612
msgid "days"
msgstr ""

#: react_app/components/pages/reviews/index.jsx:30
#: assets/js/reviewkit.core.min.js:910
msgid "Trustpilot URL"
msgstr ""

#: react_app/components/pages/reviews/index.jsx:32
#: assets/js/reviewkit.core.min.js:912
msgid "Enter the URL of your Trustpilot page"
msgstr ""

#: react_app/components/pages/reviews/index.jsx:43
#: assets/js/reviewkit.core.min.js:923
msgid "Fetch Reviews"
msgstr ""

#: react_app/components/pages/reviews/index.jsx:50
#: assets/js/reviewkit.core.min.js:930
msgid "Revalidate Reviews"
msgstr ""

#: react_app/components/pages/shortcodes/index.jsx:182
#: assets/js/reviewkit.core.min.js:1137
msgid "Trustpilot Shortcodes"
msgstr ""

#: react_app/components/pages/shortcodes/index.jsx:184
#: assets/js/reviewkit.core.min.js:1139
msgid "Use these shortcodes to display Trustpilot widgets on your website."
msgstr ""

#: react_app/components/pages/shortcodes/index.jsx:195
#: assets/js/reviewkit.core.min.js:1150
msgid "Search by name, description, or shortcode..."
msgstr ""

#: react_app/components/pages/shortcodes/index.jsx:240
#: assets/js/reviewkit.core.min.js:1195
msgid "Copied!"
msgstr ""

#: react_app/components/pages/shortcodes/index.jsx:241
#: assets/js/reviewkit.core.min.js:1196
msgid "Copy"
msgstr ""

#: react_app/components/pages/shortcodes/index.jsx:268
#: assets/js/reviewkit.core.min.js:1223
msgid "No shortcodes found matching your search."
msgstr ""

#: react_app/components/shortcode-previews/MicroButtonPreview.jsx:17
#: react_app/components/shortcode-previews/MicroComboPreview.jsx:40
#: react_app/components/shortcode-previews/MicroReviewCountPreview.jsx:20
#: react_app/components/shortcode-previews/MicroStarPreview.jsx:27
#: react_app/components/shortcode-previews/MiniTrustboxPreview.jsx:23
#: assets/js/reviewkit.core.min.js:1248
#: assets/js/reviewkit.core.min.js:1299
#: assets/js/reviewkit.core.min.js:1326
#: assets/js/reviewkit.core.min.js:1360
#: assets/js/reviewkit.core.min.js:1461
msgid "Trustpilot"
msgstr ""

#: react_app/components/shortcode-previews/MicroComboPreview.jsx:38
#: react_app/components/shortcode-previews/MicroReviewCountPreview.jsx:18
#: assets/js/reviewkit.core.min.js:1297
#: assets/js/reviewkit.core.min.js:1324
msgid "reviews on"
msgstr ""

#: react_app/components/shortcode-previews/MicroReviewCountPreview.jsx:16
#: assets/js/reviewkit.core.min.js:1322
msgid "See our"
msgstr ""

#: react_app/components/shortcode-previews/MiniTrustboxPreview.jsx:37
#: assets/js/reviewkit.core.min.js:1475
msgid "TrustScore"
msgstr ""

#: react_app/components/shortcode-previews/StarterTrustboxPreview.jsx:20
#: assets/js/reviewkit.core.min.js:1515
msgid "Check out our"
msgstr ""

#: react_app/components/shortcode-previews/StarterTrustboxPreview.jsx:54
#: assets/js/reviewkit.core.min.js:1549
msgid "Helping each other make better choices"
msgstr ""

#: react_app/components/shortcode-previews/StarterTrustboxPreview.jsx:57
#: assets/js/reviewkit.core.min.js:1552
msgid "Read and write reviews"
msgstr ""
