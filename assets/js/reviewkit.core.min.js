/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./react_app/components/App.jsx":
/*!**************************************!*\
  !*** ./react_app/components/App.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _pages_Layout__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pages/Layout */ "./react_app/components/pages/Layout.jsx");
/* harmony import */ var _context_data_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context/data-context */ "./react_app/components/context/data-context.jsx");
/* harmony import */ var _pages_reviews__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./pages/reviews */ "./react_app/components/pages/reviews/index.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);




const App = () => {
  const {
    tab
  } = (0,_context_data_context__WEBPACK_IMPORTED_MODULE_1__.useDataContext)();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_pages_Layout__WEBPACK_IMPORTED_MODULE_0__["default"], {}), tab === "dashboard" && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_pages_reviews__WEBPACK_IMPORTED_MODULE_2__["default"], {})]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);

/***/ }),

/***/ "./react_app/components/context/data-context.jsx":
/*!*******************************************************!*\
  !*** ./react_app/components/context/data-context.jsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataContextProvider: () => (/* binding */ DataContextProvider),
/* harmony export */   useDataContext: () => (/* binding */ useDataContext)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);

const DataContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();


function DataContextProvider({
  children
}) {
  const [tab, setTab] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)("dashboard");
  const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_helper_utils__WEBPACK_IMPORTED_MODULE_1__.data || {});
  const value = {
    tab,
    setTab,
    data,
    setData
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(DataContext.Provider, {
    value: value,
    children: children
  });
}
function useDataContext() {
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DataContext);
}


/***/ }),

/***/ "./react_app/components/helper/utils.js":
/*!**********************************************!*\
  !*** ./react_app/components/helper/utils.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ajaxurl: () => (/* binding */ ajaxurl),
/* harmony export */   count: () => (/* binding */ count),
/* harmony export */   data: () => (/* binding */ data),
/* harmony export */   domain: () => (/* binding */ domain),
/* harmony export */   getReviews: () => (/* binding */ getReviews),
/* harmony export */   getReviewsFromAPI: () => (/* binding */ getReviewsFromAPI),
/* harmony export */   last_updated: () => (/* binding */ last_updated),
/* harmony export */   nonce: () => (/* binding */ nonce),
/* harmony export */   original_domain: () => (/* binding */ original_domain),
/* harmony export */   plugin_root_url: () => (/* binding */ plugin_root_url)
/* harmony export */ });
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__);

const {
  ajaxurl,
  nonce,
  plugin_root_url,
  data,
  count,
  last_updated,
  domain,
  original_domain
} = window.reviewkit;
/**
 * Utility functions for the ReviewKit
 */

/**
 * Fetches business reviews for a specified domain using direct API call
 *
 * @param {string} domain - The domain to fetch reviews for (e.g., "divinext.com")
 * @param {Object} options - Optional configuration options
 * @param {number} options.timeout - Request timeout in milliseconds (default: 10000)
 * @param {AbortSignal} options.signal - AbortController signal for cancelling the request
 * @returns {Promise<Object>} - Promise that resolves to the reviews data
 * @throws {Error} - Throws an error if the request fails
 */
const getReviewsFromAPI = async (domain, options = {}) => {
  if (!domain) {
    throw new Error("Domain is required");
  }

  // Remove any protocol and trailing slashes to ensure consistent format
  const cleanDomain = domain.replace(/^(https?:\/\/)?(www\.)?/, "").replace(/\/$/, "");
  const {
    timeout = 10000,
    signal
  } = options;
  try {
    // Create a controller for timeout if not provided externally
    const controller = signal ? null : new AbortController();
    const timeoutId = signal ? null : setTimeout(() => controller.abort(), timeout);
    const response = await fetch(`https://api.gutensuite.net/data/tp/v1/reviews/${encodeURIComponent(cleanDomain)}`, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json"
      },
      signal: signal || controller.signal
    });

    // Clear timeout if we created one
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    if (!response.ok) {
      throw new Error(`Failed to fetch reviews: ${response.status} ${response.statusText}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    if (error.name === "AbortError") {
      throw new Error("Request timed out");
    }
    throw error;
  }
};

/**
 * Fetches business reviews for a specified domain using WordPress AJAX
 *
 * @param {string} domain - The domain to fetch reviews for (e.g., "divinext.com")
 * @param {Object} options - Optional configuration options
 * @param {number} options.timeout - Request timeout in milliseconds (default: 30000)
 * @returns {Promise<Object>} - Promise that resolves to the reviews data
 * @throws {Error} - Throws an error if the request fails
 */
const getReviews = (domain, options = {}) => {
  if (!domain) {
    return Promise.reject(new Error("Domain is required"));
  }
  const {
    timeout = 30000
  } = options;

  // Ensure we have access to WordPress ajax functionality
  if (typeof window.ajaxurl === "undefined" || typeof window.reviewkit === "undefined") {
    return Promise.reject(new Error("WordPress AJAX not available. Make sure the script is properly enqueued."));
  }
  return new Promise((resolve, reject) => {
    // Create a FormData object for the request
    const formData = new FormData();
    formData.append("action", "reviewkit_get_reviews");
    formData.append("security", nonce);
    formData.append("domain", domain);
    formData.append("revalidate", options.revalidate);
    console.info(domain, options);

    // Use jQuery if available, otherwise use fetch
    if (window.jQuery) {
      jQuery.ajax({
        url: window.ajaxurl,
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        timeout: timeout,
        success: function (response) {
          console.info(response);
          if (response.success) {
            resolve(response.data);
          } else {
            reject(new Error(response.data?.message || "Unknown error occurred"));
          }
        },
        error: function (xhr, status, error) {
          if (status === "timeout") {
            reject(new Error("Request timed out"));
          } else {
            reject(new Error(`AJAX request failed: ${error}`));
          }
        }
      });
    } else {
      // Fallback to using fetch API if jQuery is not available
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      fetch(window.ajaxurl, {
        method: "POST",
        body: formData,
        signal: controller.signal
      }).then(response => {
        clearTimeout(timeoutId);
        if (!response.ok) {
          throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        return response.json();
      }).then(response => {
        if (response.success) {
          resolve(response.data);
        } else {
          reject(new Error(response.data?.message || "Unknown error occurred"));
        }
      }).catch(error => {
        clearTimeout(timeoutId);
        if (error.name === "AbortError") {
          reject(new Error("Request timed out"));
        } else {
          reject(error);
        }
      });
    }
  });
};

/***/ }),

/***/ "./react_app/components/pages/Layout.jsx":
/*!***********************************************!*\
  !*** ./react_app/components/pages/Layout.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _context_data_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context/data-context */ "./react_app/components/context/data-context.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__);


const Layout = () => {
  const {
    tab,
    setTab
  } = (0,_context_data_context__WEBPACK_IMPORTED_MODULE_0__.useDataContext)();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("nav", {
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("ul", {
      children: menuItems.map(item => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("li", {
        className: `${tab === item.tab ? "active" : ""}`,
        onClick: () => setTab(item.tab),
        children: item.label
      }, item.label))
    })
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);
const menuItems = [{
  label: "Dashboard",
  tab: "dashboard"
}];

/***/ }),

/***/ "./react_app/components/pages/reviews/index.jsx":
/*!******************************************************!*\
  !*** ./react_app/components/pages/reviews/index.jsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/components */ "@wordpress/components");
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _context_data_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/data-context */ "./react_app/components/context/data-context.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);






const Reviews = () => {
  const {
    data = {},
    setData
  } = (0,_context_data_context__WEBPACK_IMPORTED_MODULE_4__.useDataContext)();
  const {
    business_details = {},
    reviews = []
  } = data;
  const [businessUrl, setBusinessUrl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_helper_utils__WEBPACK_IMPORTED_MODULE_3__.original_domain);
  const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const __fetchReviews = async (revalidate = false) => {
    setLoading(true);
    const response = await (0,_helper_utils__WEBPACK_IMPORTED_MODULE_3__.getReviews)(businessUrl, {
      revalidate: revalidate
    });
    if (response?.data) {
      setData(response.data);
    }
    setLoading(false);
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div", {
    className: "page review-page",
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)("div", {
      className: "review-fetch",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.TextControl, {
        __nextHasNoMarginBottom: true,
        __next40pxDefaultSize: true,
        label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Business URL", "reviewkit"),
        type: "url",
        help: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Enter the URL of your business page", "reviewkit"),
        value: businessUrl,
        onChange: value => setBusinessUrl(value),
        placeholder: "Your Business Page URL"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)("div", {
        className: "button-container",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Button, {
          variant: "primary",
          style: {
            backgroundColor: "#28a745",
            fontWeight: "600"
          },
          onClick: __fetchReviews,
          disabled: businessUrl === "",
          children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Fetch Reviews", "reviewkit")
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Button, {
          variant: "secondary",
          onClick: () => __fetchReviews(true),
          disabled: !data || businessUrl === "",
          children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Sync Reviews", "reviewkit")
        })]
      })]
    })
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Reviews);

/***/ }),

/***/ "@wordpress/components":
/*!************************************!*\
  !*** external ["wp","components"] ***!
  \************************************/
/***/ ((module) => {

module.exports = window["wp"]["components"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "React" ***!
  \************************/
/***/ ((module) => {

module.exports = window["React"];

/***/ }),

/***/ "react-dom":
/*!***************************!*\
  !*** external "ReactDOM" ***!
  \***************************/
/***/ ((module) => {

module.exports = window["ReactDOM"];

/***/ }),

/***/ "react/jsx-runtime":
/*!**********************************!*\
  !*** external "ReactJSXRuntime" ***!
  \**********************************/
/***/ ((module) => {

module.exports = window["ReactJSXRuntime"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!****************************!*\
  !*** ./react_app/index.js ***!
  \****************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ "react-dom");
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _components_App__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/App */ "./react_app/components/App.jsx");
/* harmony import */ var _components_context_data_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/context/data-context */ "./react_app/components/context/data-context.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);




document.addEventListener("DOMContentLoaded", function () {
  const body = document.getElementById("tp-reviewskit-body");
  const root = (0,react_dom__WEBPACK_IMPORTED_MODULE_0__.createRoot)(body);
  root.render(/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_components_context_data_context__WEBPACK_IMPORTED_MODULE_2__.DataContextProvider, {
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_components_App__WEBPACK_IMPORTED_MODULE_1__["default"], {})
  }));
});
})();

/******/ })()
;
//# sourceMappingURL=reviewkit.core.min.js.map