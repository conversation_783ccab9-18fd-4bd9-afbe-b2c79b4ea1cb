{"version": 3, "file": "reviewkit.core.min.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAoC;AACoB;AAClB;AAAA;AAEtC,MAAMO,GAAG,GAAGA,CAAA,KAAM;EAChB,MAAM;IAAEC;EAAI,CAAC,GAAGP,qEAAc,CAAC,CAAC;EAChC,oBACAK,uDAAA;IAAAG,QAAA,gBACCL,sDAAA,CAACJ,qDAAM,IAAE,CAAC,EACTQ,GAAG,KAAK,WAAW,iBAAIJ,sDAAA,CAACF,sDAAO,IAAE,CAAC;EAAA,CAC/B,CAAC;AAER,CAAC;AAED,iEAAeK,GAAG,E;;;;;;;;;;;;;;;;;;;;ACdiD;AACnE,MAAMO,WAAW,GAAGH,oDAAa,CAAC,CAAC;AACqB;AAAA;AAExD,SAASM,mBAAmBA,CAAC;EAAER;AAAS,CAAC,EAAE;EACzC,MAAM,CAACD,GAAG,EAAEU,MAAM,CAAC,GAAGL,+CAAQ,CAAC,WAAW,CAAC;EAC3C,MAAM,CAACE,IAAI,EAAEI,OAAO,CAAC,GAAGN,+CAAQ,CAACG,+CAAa,IAAI,CAAC,CAAC,CAAC;EAErD,MAAMI,KAAK,GAAG;IACZZ,GAAG;IACHU,MAAM;IACNH,IAAI;IACJI;EACF,CAAC;EAED,oBAAOf,sDAAA,CAACU,WAAW,CAACO,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAX,QAAA,EAAEA;EAAQ,CAAuB,CAAC;AAC9E;AAEA,SAASR,cAAcA,CAAA,EAAG;EACxB,OAAOW,iDAAU,CAACE,WAAW,CAAC;AAChC;;;;;;;;;;;;;;;;;;;;;;;;;;ACpBqC;AAE9B,MAAM;EACZS,OAAO;EACPC,KAAK;EACLC,eAAe;EACfV,IAAI;EACJW,KAAK;EACLC,YAAY;EACZC,MAAM;EACNC;AACD,CAAC,GAAGC,MAAM,CAACC,SAAS;AACpB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,iBAAiB,GAAG,MAAAA,CAAOJ,MAAM,EAAEK,OAAO,GAAG,CAAC,CAAC,KAAK;EAChE,IAAI,CAACL,MAAM,EAAE;IACZ,MAAM,IAAIM,KAAK,CAAC,oBAAoB,CAAC;EACtC;;EAEA;EACA,MAAMC,WAAW,GAAGP,MAAM,CACxBQ,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC,CACtCA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAEpB,MAAM;IAAEC,OAAO,GAAG,KAAK;IAAEC;EAAO,CAAC,GAAGL,OAAO;EAE3C,IAAI;IACH;IACA,MAAMM,UAAU,GAAGD,MAAM,GAAG,IAAI,GAAG,IAAIE,eAAe,CAAC,CAAC;IACxD,MAAMC,SAAS,GAAGH,MAAM,GACrB,IAAI,GACJI,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAEN,OAAO,CAAC;IAEhD,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAC3B,iDAAiDC,kBAAkB,CAClEX,WACD,CAAC,EAAE,EACH;MACCY,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACRC,MAAM,EAAE,kBAAkB;QAC1B,cAAc,EAAE;MACjB,CAAC;MACDX,MAAM,EAAEA,MAAM,IAAIC,UAAU,CAACD;IAC9B,CACD,CAAC;;IAED;IACA,IAAIG,SAAS,EAAE;MACdS,YAAY,CAACT,SAAS,CAAC;IACxB;IAEA,IAAI,CAACG,QAAQ,CAACO,EAAE,EAAE;MACjB,MAAM,IAAIjB,KAAK,CACd,4BAA4BU,QAAQ,CAACQ,MAAM,IAAIR,QAAQ,CAACS,UAAU,EACnE,CAAC;IACF;IAEA,MAAMtC,IAAI,GAAG,MAAM6B,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOvC,IAAI;EACZ,CAAC,CAAC,OAAOwC,KAAK,EAAE;IACf,IAAIA,KAAK,CAACC,IAAI,KAAK,YAAY,EAAE;MAChC,MAAM,IAAItB,KAAK,CAAC,mBAAmB,CAAC;IACrC;IACA,MAAMqB,KAAK;EACZ;AACD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAME,UAAU,GAAGA,CAAC7B,MAAM,EAAEK,OAAO,GAAG,CAAC,CAAC,KAAK;EACnD,IAAI,CAACL,MAAM,EAAE;IACZ,OAAO8B,OAAO,CAACC,MAAM,CAAC,IAAIzB,KAAK,CAAC,oBAAoB,CAAC,CAAC;EACvD;EAEA,MAAM;IAAEG,OAAO,GAAG;EAAM,CAAC,GAAGJ,OAAO;;EAEnC;EACA,IACC,OAAOH,MAAM,CAACP,OAAO,KAAK,WAAW,IACrC,OAAOO,MAAM,CAACC,SAAS,KAAK,WAAW,EACtC;IACD,OAAO2B,OAAO,CAACC,MAAM,CACpB,IAAIzB,KAAK,CACR,0EACD,CACD,CAAC;EACF;EAEA,OAAO,IAAIwB,OAAO,CAAC,CAACE,OAAO,EAAED,MAAM,KAAK;IACvC;IACA,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,uBAAuB,CAAC;IAClDF,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEvC,KAAK,CAAC;IAClCqC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEnC,MAAM,CAAC;IACjCiC,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE9B,OAAO,CAAC+B,UAAU,CAAC;IAEjDC,OAAO,CAACC,IAAI,CAACtC,MAAM,EAAEK,OAAO,CAAC;;IAE7B;IACA,IAAIH,MAAM,CAACqC,MAAM,EAAE;MAClBA,MAAM,CAACC,IAAI,CAAC;QACXC,GAAG,EAAEvC,MAAM,CAACP,OAAO;QACnB+C,IAAI,EAAE,MAAM;QACZvD,IAAI,EAAE8C,QAAQ;QACdU,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,KAAK;QAClBnC,OAAO,EAAEA,OAAO;QAChBoC,OAAO,EAAE,SAAAA,CAAU7B,QAAQ,EAAE;UAC5BqB,OAAO,CAACC,IAAI,CAACtB,QAAQ,CAAC;UACtB,IAAIA,QAAQ,CAAC6B,OAAO,EAAE;YACrBb,OAAO,CAAChB,QAAQ,CAAC7B,IAAI,CAAC;UACvB,CAAC,MAAM;YACN4C,MAAM,CACL,IAAIzB,KAAK,CAACU,QAAQ,CAAC7B,IAAI,EAAE2D,OAAO,IAAI,wBAAwB,CAC7D,CAAC;UACF;QACD,CAAC;QACDnB,KAAK,EAAE,SAAAA,CAAUoB,GAAG,EAAEvB,MAAM,EAAEG,KAAK,EAAE;UACpC,IAAIH,MAAM,KAAK,SAAS,EAAE;YACzBO,MAAM,CAAC,IAAIzB,KAAK,CAAC,mBAAmB,CAAC,CAAC;UACvC,CAAC,MAAM;YACNyB,MAAM,CAAC,IAAIzB,KAAK,CAAC,wBAAwBqB,KAAK,EAAE,CAAC,CAAC;UACnD;QACD;MACD,CAAC,CAAC;IACH,CAAC,MAAM;MACN;MACA,MAAMhB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAEN,OAAO,CAAC;MAE/DQ,KAAK,CAACf,MAAM,CAACP,OAAO,EAAE;QACrBwB,MAAM,EAAE,MAAM;QACd6B,IAAI,EAAEf,QAAQ;QACdvB,MAAM,EAAEC,UAAU,CAACD;MACpB,CAAC,CAAC,CACAuC,IAAI,CAAEjC,QAAQ,IAAK;QACnBM,YAAY,CAACT,SAAS,CAAC;QACvB,IAAI,CAACG,QAAQ,CAACO,EAAE,EAAE;UACjB,MAAM,IAAIjB,KAAK,CACd,mBAAmBU,QAAQ,CAACQ,MAAM,KAAKR,QAAQ,CAACS,UAAU,EAC3D,CAAC;QACF;QACA,OAAOT,QAAQ,CAACU,IAAI,CAAC,CAAC;MACvB,CAAC,CAAC,CACDuB,IAAI,CAAEjC,QAAQ,IAAK;QACnB,IAAIA,QAAQ,CAAC6B,OAAO,EAAE;UACrBb,OAAO,CAAChB,QAAQ,CAAC7B,IAAI,CAAC;QACvB,CAAC,MAAM;UACN4C,MAAM,CACL,IAAIzB,KAAK,CAACU,QAAQ,CAAC7B,IAAI,EAAE2D,OAAO,IAAI,wBAAwB,CAC7D,CAAC;QACF;MACD,CAAC,CAAC,CACDI,KAAK,CAAEvB,KAAK,IAAK;QACjBL,YAAY,CAACT,SAAS,CAAC;QACvB,IAAIc,KAAK,CAACC,IAAI,KAAK,YAAY,EAAE;UAChCG,MAAM,CAAC,IAAIzB,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC,MAAM;UACNyB,MAAM,CAACJ,KAAK,CAAC;QACd;MACD,CAAC,CAAC;IACJ;EACD,CAAC,CAAC;AACH,CAAC,C;;;;;;;;;;;;;;;;;ACvLwD;AAAA;AAEzD,MAAMvD,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAM;IAAEQ,GAAG;IAAEU;EAAO,CAAC,GAAGjB,qEAAc,CAAC,CAAC;EAExC,oBACEG,sDAAA;IAAAK,QAAA,eACEL,sDAAA;MAAAK,QAAA,EACGsE,SAAS,CAACC,GAAG,CAAEC,IAAI,iBAClB7E,sDAAA;QAEE8E,SAAS,EAAE,GAAG1E,GAAG,KAAKyE,IAAI,CAACzE,GAAG,GAAG,QAAQ,GAAG,EAAE,EAAG;QACjD2E,OAAO,EAAEA,CAAA,KAAMjE,MAAM,CAAC+D,IAAI,CAACzE,GAAG,CAAE;QAAAC,QAAA,EAE/BwE,IAAI,CAACG;MAAK,GAJNH,IAAI,CAACG,KAKR,CACL;IAAC,CACA;EAAC,CACF,CAAC;AAEV,CAAC;AAED,iEAAepF,MAAM,EAAC;AAEtB,MAAM+E,SAAS,GAAG,CAAC;EAAEK,KAAK,EAAE,WAAW;EAAE5E,GAAG,EAAE;AAAY,CAAC,CAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACxB3B;AACI;AACgC;AACJ;AACL;AAAA;AAE5D,MAAMN,OAAO,GAAGA,CAAA,KAAM;EACrB,MAAM;IAAEa,IAAI,GAAG,CAAC,CAAC;IAAEI;EAAQ,CAAC,GAAGlB,qEAAc,CAAC,CAAC;EAC/C,MAAM;IAAEuF,gBAAgB,GAAG,CAAC,CAAC;IAAEC,OAAO,GAAG;EAAG,CAAC,GAAG1E,IAAI;EACpD,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,+CAAQ,CAACgB,0DAAe,CAAC;EAC/D,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhF,+CAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMiF,cAAc,GAAG,MAAAA,CAAO9B,UAAU,GAAG,KAAK,KAAK;IACpD6B,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMjD,QAAQ,GAAG,MAAMa,yDAAU,CAACiC,WAAW,EAAE;MAAE1B,UAAU,EAAEA;IAAW,CAAC,CAAC;IAC1E,IAAIpB,QAAQ,EAAE7B,IAAI,EAAE;MACnBI,OAAO,CAACyB,QAAQ,CAAC7B,IAAI,CAAC;IACvB;IACA8E,UAAU,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,oBACCzF,sDAAA;IAAK8E,SAAS,EAAC,kBAAkB;IAAAzE,QAAA,eAChCH,uDAAA;MAAK4E,SAAS,EAAC,cAAc;MAAAzE,QAAA,gBAC5BL,sDAAA,CAACiF,8DAAW;QACXU,uBAAuB;QACvBC,qBAAqB;QACrBZ,KAAK,EAAE9D,mDAAE,CAAC,cAAc,EAAE,WAAW,CAAE;QACvCgD,IAAI,EAAC,KAAK;QACV2B,IAAI,EAAE3E,mDAAE,CAAC,qCAAqC,EAAE,WAAW,CAAE;QAC7DF,KAAK,EAAEsE,WAAY;QACnBQ,QAAQ,EAAG9E,KAAK,IAAKuE,cAAc,CAACvE,KAAK,CAAE;QAC3C+E,WAAW,EAAC;MAAwB,CACpC,CAAC,eACF7F,uDAAA;QAAK4E,SAAS,EAAC,kBAAkB;QAAAzE,QAAA,gBAChCL,sDAAA,CAACkF,yDAAM;UACNc,OAAO,EAAC,SAAS;UACjBC,KAAK,EAAE;YAAEC,eAAe,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UACzDpB,OAAO,EAAEW,cAAe;UACxBU,QAAQ,EAAEd,WAAW,KAAK,EAAG;UAAAjF,QAAA,EAE5Ba,mDAAE,CAAC,eAAe,EAAE,WAAW;QAAC,CAC1B,CAAC,eACTlB,sDAAA,CAACkF,yDAAM;UACNc,OAAO,EAAC,WAAW;UACnBjB,OAAO,EAAEA,CAAA,KAAMW,cAAc,CAAC,IAAI,CAAE;UACpCU,QAAQ,EAAE,CAACzF,IAAI,IAAI2E,WAAW,KAAK,EAAG;UAAAjF,QAAA,EAErCa,mDAAE,CAAC,cAAc,EAAE,WAAW;QAAC,CACzB,CAAC;MAAA,CACL,CAAC;IAAA,CACF;EAAC,CACF,CAAC;AAER,CAAC;AAED,iEAAepB,OAAO,E;;;;;;;;;;ACxDtB,4C;;;;;;;;;;ACAA,sC;;;;;;;;;;ACAA,iC;;;;;;;;;;ACAA,oC;;;;;;;;;;ACAA,2C;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;ACNuC;AACJ;AACqC;AAAA;AAExEwG,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,YAAY;EACzD,MAAM/B,IAAI,GAAG8B,QAAQ,CAACE,cAAc,CAAC,oBAAoB,CAAC;EAC1D,MAAMC,IAAI,GAAGJ,qDAAU,CAAC7B,IAAI,CAAC;EAE7BiC,IAAI,CAACC,MAAM,cACV1G,sDAAA,CAACa,iFAAmB;IAAAR,QAAA,eACnBL,sDAAA,CAACG,uDAAG,IAAE;EAAC,CACa,CACtB,CAAC;AACF,CAAC,CAAC,C", "sources": ["webpack://trustpilot-reviewkit/./react_app/components/App.jsx", "webpack://trustpilot-reviewkit/./react_app/components/context/data-context.jsx", "webpack://trustpilot-reviewkit/./react_app/components/helper/utils.js", "webpack://trustpilot-reviewkit/./react_app/components/pages/Layout.jsx", "webpack://trustpilot-reviewkit/./react_app/components/pages/reviews/index.jsx", "webpack://trustpilot-reviewkit/external window [\"wp\",\"components\"]", "webpack://trustpilot-reviewkit/external window [\"wp\",\"i18n\"]", "webpack://trustpilot-reviewkit/external window \"React\"", "webpack://trustpilot-reviewkit/external window \"ReactDOM\"", "webpack://trustpilot-reviewkit/external window \"ReactJSXRuntime\"", "webpack://trustpilot-reviewkit/webpack/bootstrap", "webpack://trustpilot-reviewkit/webpack/runtime/compat get default export", "webpack://trustpilot-reviewkit/webpack/runtime/define property getters", "webpack://trustpilot-reviewkit/webpack/runtime/hasOwnProperty shorthand", "webpack://trustpilot-reviewkit/webpack/runtime/make namespace object", "webpack://trustpilot-reviewkit/./react_app/index.js"], "sourcesContent": ["import Layout from \"./pages/Layout\";\nimport { useDataContext } from \"./context/data-context\";\nimport Reviews from \"./pages/reviews\";\n\nconst App = () => {\n  const { tab } = useDataContext();\n  return (\n\t\t<div>\n\t\t\t<Layout />\n\t\t\t{tab === \"dashboard\" && <Reviews />}\n\t\t</div>\n\t);\n};\n\nexport default App;\n", "import React, { createContext, useContext, useState } from \"react\";\nconst DataContext = createContext();\nimport { data as reviewDetails } from \"../helper/utils\";\n\nfunction DataContextProvider({ children }) {\n  const [tab, setTab] = useState(\"dashboard\");\n  const [data, setData] = useState(reviewDetails || {});\n\n  const value = {\n    tab,\n    setTab,\n    data,\n    setData,\n  };\n\n  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;\n}\n\nfunction useDataContext() {\n  return useContext(DataContext);\n}\n\nexport { DataContextProvider, useDataContext };\n", "import { __ } from \"@wordpress/i18n\";\n\nexport const {\n\tajaxurl,\n\tnonce,\n\tplugin_root_url,\n\tdata,\n\tcount,\n\tlast_updated,\n\tdomain,\n\toriginal_domain,\n} = window.reviewkit;\n/**\n * Utility functions for the ReviewKit\n */\n\n/**\n * Fetches business reviews for a specified domain using direct API call\n *\n * @param {string} domain - The domain to fetch reviews for (e.g., \"divinext.com\")\n * @param {Object} options - Optional configuration options\n * @param {number} options.timeout - Request timeout in milliseconds (default: 10000)\n * @param {AbortSignal} options.signal - AbortController signal for cancelling the request\n * @returns {Promise<Object>} - Promise that resolves to the reviews data\n * @throws {Error} - Throws an error if the request fails\n */\nexport const getReviewsFromAPI = async (domain, options = {}) => {\n\tif (!domain) {\n\t\tthrow new Error(\"Domain is required\");\n\t}\n\n\t// Remove any protocol and trailing slashes to ensure consistent format\n\tconst cleanDomain = domain\n\t\t.replace(/^(https?:\\/\\/)?(www\\.)?/, \"\")\n\t\t.replace(/\\/$/, \"\");\n\n\tconst { timeout = 10000, signal } = options;\n\n\ttry {\n\t\t// Create a controller for timeout if not provided externally\n\t\tconst controller = signal ? null : new AbortController();\n\t\tconst timeoutId = signal\n\t\t\t? null\n\t\t\t: setTimeout(() => controller.abort(), timeout);\n\n\t\tconst response = await fetch(\n\t\t\t`https://api.gutensuite.net/data/tp/v1/reviews/${encodeURIComponent(\n\t\t\t\tcleanDomain\n\t\t\t)}`,\n\t\t\t{\n\t\t\t\tmethod: \"GET\",\n\t\t\t\theaders: {\n\t\t\t\t\tAccept: \"application/json\",\n\t\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t},\n\t\t\t\tsignal: signal || controller.signal,\n\t\t\t}\n\t\t);\n\n\t\t// Clear timeout if we created one\n\t\tif (timeoutId) {\n\t\t\tclearTimeout(timeoutId);\n\t\t}\n\n\t\tif (!response.ok) {\n\t\t\tthrow new Error(\n\t\t\t\t`Failed to fetch reviews: ${response.status} ${response.statusText}`\n\t\t\t);\n\t\t}\n\n\t\tconst data = await response.json();\n\t\treturn data;\n\t} catch (error) {\n\t\tif (error.name === \"AbortError\") {\n\t\t\tthrow new Error(\"Request timed out\");\n\t\t}\n\t\tthrow error;\n\t}\n};\n\n/**\n * Fetches business reviews for a specified domain using WordPress AJAX\n *\n * @param {string} domain - The domain to fetch reviews for (e.g., \"divinext.com\")\n * @param {Object} options - Optional configuration options\n * @param {number} options.timeout - Request timeout in milliseconds (default: 30000)\n * @returns {Promise<Object>} - Promise that resolves to the reviews data\n * @throws {Error} - Throws an error if the request fails\n */\nexport const getReviews = (domain, options = {}) => {\n\tif (!domain) {\n\t\treturn Promise.reject(new Error(\"Domain is required\"));\n\t}\n\n\tconst { timeout = 30000 } = options;\n\n\t// Ensure we have access to WordPress ajax functionality\n\tif (\n\t\ttypeof window.ajaxurl === \"undefined\" ||\n\t\ttypeof window.reviewkit === \"undefined\"\n\t) {\n\t\treturn Promise.reject(\n\t\t\tnew Error(\n\t\t\t\t\"WordPress AJAX not available. Make sure the script is properly enqueued.\"\n\t\t\t)\n\t\t);\n\t}\n\n\treturn new Promise((resolve, reject) => {\n\t\t// Create a FormData object for the request\n\t\tconst formData = new FormData();\n\t\tformData.append(\"action\", \"reviewkit_get_reviews\");\n\t\tformData.append(\"security\", nonce);\n\t\tformData.append(\"domain\", domain);\n\t\tformData.append(\"revalidate\", options.revalidate);\n\n\t\tconsole.info(domain, options);\n\n\t\t// Use jQuery if available, otherwise use fetch\n\t\tif (window.jQuery) {\n\t\t\tjQuery.ajax({\n\t\t\t\turl: window.ajaxurl,\n\t\t\t\ttype: \"POST\",\n\t\t\t\tdata: formData,\n\t\t\t\tprocessData: false,\n\t\t\t\tcontentType: false,\n\t\t\t\ttimeout: timeout,\n\t\t\t\tsuccess: function (response) {\n\t\t\t\t\tconsole.info(response);\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tresolve(response.data);\n\t\t\t\t\t} else {\n\t\t\t\t\t\treject(\n\t\t\t\t\t\t\tnew Error(response.data?.message || \"Unknown error occurred\")\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\terror: function (xhr, status, error) {\n\t\t\t\t\tif (status === \"timeout\") {\n\t\t\t\t\t\treject(new Error(\"Request timed out\"));\n\t\t\t\t\t} else {\n\t\t\t\t\t\treject(new Error(`AJAX request failed: ${error}`));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t});\n\t\t} else {\n\t\t\t// Fallback to using fetch API if jQuery is not available\n\t\t\tconst controller = new AbortController();\n\t\t\tconst timeoutId = setTimeout(() => controller.abort(), timeout);\n\n\t\t\tfetch(window.ajaxurl, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: formData,\n\t\t\t\tsignal: controller.signal,\n\t\t\t})\n\t\t\t\t.then((response) => {\n\t\t\t\t\tclearTimeout(timeoutId);\n\t\t\t\t\tif (!response.ok) {\n\t\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t\t`Server returned ${response.status}: ${response.statusText}`\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\treturn response.json();\n\t\t\t\t})\n\t\t\t\t.then((response) => {\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tresolve(response.data);\n\t\t\t\t\t} else {\n\t\t\t\t\t\treject(\n\t\t\t\t\t\t\tnew Error(response.data?.message || \"Unknown error occurred\")\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.catch((error) => {\n\t\t\t\t\tclearTimeout(timeoutId);\n\t\t\t\t\tif (error.name === \"AbortError\") {\n\t\t\t\t\t\treject(new Error(\"Request timed out\"));\n\t\t\t\t\t} else {\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t}\n\t});\n};\n", "import { useDataContext } from \"../context/data-context\";\n\nconst Layout = () => {\n  const { tab, setTab } = useDataContext();\n\n  return (\n    <nav>\n      <ul>\n        {menuItems.map((item) => (\n          <li\n            key={item.label}\n            className={`${tab === item.tab ? \"active\" : \"\"}`}\n            onClick={() => setTab(item.tab)}\n          >\n            {item.label}\n          </li>\n        ))}\n      </ul>\n    </nav>\n  );\n};\n\nexport default Layout;\n\nconst menuItems = [{ label: \"Dashboard\", tab: \"dashboard\" }];\n", "import { useState } from \"react\";\nimport { __ } from \"@wordpress/i18n\";\nimport { TextControl, Button, Spinner } from \"@wordpress/components\";\nimport { getReviews, original_domain } from \"../../helper/utils\";\nimport { useDataContext } from \"../../context/data-context\";\n\nconst Reviews = () => {\n\tconst { data = {}, setData } = useDataContext();\n\tconst { business_details = {}, reviews = [] } = data;\n\tconst [businessUrl, setBusinessUrl] = useState(original_domain);\n\tconst [loading, setLoading] = useState(false);\n\n\tconst __fetchReviews = async (revalidate = false) => {\n\t\tsetLoading(true);\n\t\tconst response = await getReviews(businessUrl, { revalidate: revalidate });\n\t\tif (response?.data) {\n\t\t\tsetData(response.data);\n\t\t}\n\t\tsetLoading(false);\n\t};\n\n\treturn (\n\t\t<div className=\"page review-page\">\n\t\t\t<div className=\"review-fetch\">\n\t\t\t\t<TextControl\n\t\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\t\t__next40pxDefaultSize\n\t\t\t\t\tlabel={__(\"Business URL\", \"reviewkit\")}\n\t\t\t\t\ttype=\"url\"\n\t\t\t\t\thelp={__(\"Enter the URL of your business page\", \"reviewkit\")}\n\t\t\t\t\tvalue={businessUrl}\n\t\t\t\t\tonChange={(value) => setBusinessUrl(value)}\n\t\t\t\t\tplaceholder=\"Your Business Page URL\"\n\t\t\t\t/>\n\t\t\t\t<div className=\"button-container\">\n\t\t\t\t\t<Button\n\t\t\t\t\t\tvariant=\"primary\"\n\t\t\t\t\t\tstyle={{ backgroundColor: \"#28a745\", fontWeight: \"600\" }}\n\t\t\t\t\t\tonClick={__fetchReviews}\n\t\t\t\t\t\tdisabled={businessUrl === \"\"}\n\t\t\t\t\t>\n\t\t\t\t\t\t{__(\"Fetch Reviews\", \"reviewkit\")}\n\t\t\t\t\t</Button>\n\t\t\t\t\t<Button\n\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\tonClick={() => __fetchReviews(true)}\n\t\t\t\t\t\tdisabled={!data || businessUrl === \"\"}\n\t\t\t\t\t>\n\t\t\t\t\t\t{__(\"Sync Reviews\", \"reviewkit\")}\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n};\n\nexport default Reviews;\n", "module.exports = window[\"wp\"][\"components\"];", "module.exports = window[\"wp\"][\"i18n\"];", "module.exports = window[\"React\"];", "module.exports = window[\"ReactDOM\"];", "module.exports = window[\"ReactJSXRuntime\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { createRoot } from \"react-dom\";\nimport App from \"./components/App\";\nimport { DataContextProvider } from \"./components/context/data-context\";\n\ndocument.addEventListener(\"DOMContentLoaded\", function () {\n\tconst body = document.getElementById(\"tp-reviewskit-body\");\n\tconst root = createRoot(body);\n\n\troot.render(\n\t\t<DataContextProvider>\n\t\t\t<App />\n\t\t</DataContextProvider>\n\t);\n});\n"], "names": ["Layout", "useDataContext", "Reviews", "jsx", "_jsx", "jsxs", "_jsxs", "App", "tab", "children", "React", "createContext", "useContext", "useState", "DataContext", "data", "reviewDetails", "DataContextProvider", "setTab", "setData", "value", "Provider", "__", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nonce", "plugin_root_url", "count", "last_updated", "domain", "original_domain", "window", "reviewkit", "getReviewsFromAPI", "options", "Error", "cleanDomain", "replace", "timeout", "signal", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "encodeURIComponent", "method", "headers", "Accept", "clearTimeout", "ok", "status", "statusText", "json", "error", "name", "getReviews", "Promise", "reject", "resolve", "formData", "FormData", "append", "revalidate", "console", "info", "j<PERSON><PERSON><PERSON>", "ajax", "url", "type", "processData", "contentType", "success", "message", "xhr", "body", "then", "catch", "menuItems", "map", "item", "className", "onClick", "label", "TextControl", "<PERSON><PERSON>", "Spinner", "business_details", "reviews", "businessUrl", "setBusinessUrl", "loading", "setLoading", "__fetchReviews", "__nextHasNoMarginBottom", "__next40pxDefaultSize", "help", "onChange", "placeholder", "variant", "style", "backgroundColor", "fontWeight", "disabled", "createRoot", "document", "addEventListener", "getElementById", "root", "render"], "sourceRoot": ""}